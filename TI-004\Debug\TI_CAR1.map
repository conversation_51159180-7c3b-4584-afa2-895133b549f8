******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 14:49:12 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000078a5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009da8  00016258  R  X
  SRAM                  20200000   00008000  00000708  000078f8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009da8   00009da8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008570   00008570    r-x .text
  00008630    00008630    00001700   00001700    r-- .rodata
  00009d30    00009d30    00000078   00000078    r-- .cinit
20200000    20200000    00000509   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000135   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008570     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001fe8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021c4    000001b0     Task.o (.text.Task_Start)
                  00002374    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002514    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000026a6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000026a8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002830    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000029b8    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002b30    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002ca0    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002de4    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002f20    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003054    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003188    00000130     OLED.o (.text.OLED_ShowChar)
                  000032b8    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000033e8    00000128     Task_App.o (.text.Task_Tracker)
                  00003510    00000128     inv_mpu.o (.text.mpu_init)
                  00003638    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  0000375c    00000124     Task_App.o (.text.Task_Init)
                  00003880    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000039a4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003ac4    00000114     Task_App.o (.text.Task_Motor_PID)
                  00003bd8    00000110     OLED.o (.text.OLED_Init)
                  00003ce8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003df4    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003efc    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004000    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004100    000000f0     Motor.o (.text.Motor_SetDirc)
                  000041f0    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  000042dc    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000043c0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000044a4    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004588    000000e0     Task_App.o (.text.Task_OLED)
                  00004668    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004744    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00004820    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000048f8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000049d0    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004aa4    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004b74    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004c38    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004cfc    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004db8    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004e70    000000b4     Task.o (.text.Task_Add)
                  00004f24    000000ac     Task_App.o (.text.Task_Serial)
                  00004fd0    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  0000507c    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005128    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000051d2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000051d4    000000a2                            : udivmoddi4.S.obj (.text)
                  00005276    00000002     --HOLE-- [fill = 0]
                  00005278    000000a0     Motor.o (.text.Motor_SetDuty)
                  00005318    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000053b8    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005454    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000054ec    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005584    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000561a    00000002     --HOLE-- [fill = 0]
                  0000561c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000056a8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005734    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  000057c0    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005844    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000058c8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000594a    00000002     --HOLE-- [fill = 0]
                  0000594c    00000080     Motor.o (.text.Motor_GetSpeed)
                  000059cc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005a48    00000074     Motor.o (.text.Motor_Start)
                  00005abc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005b30    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005ba4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005c18    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005c8a    00000002     --HOLE-- [fill = 0]
                  00005c8c    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005cfc    0000006e     OLED.o (.text.OLED_ShowString)
                  00005d6a    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00005dd6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005e40    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005ea8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005f0e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005f74    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005fd8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000603c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000609e    00000002     --HOLE-- [fill = 0]
                  000060a0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00006102    00000002     --HOLE-- [fill = 0]
                  00006104    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006164    00000060     Key_Led.o (.text.Key_Read)
                  000061c4    00000060     Task_App.o (.text.Task_IdleFunction)
                  00006224    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006284    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000062e4    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006344    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000063a2    00000002     --HOLE-- [fill = 0]
                  000063a4    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006400    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000645c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000064b8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006514    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000656c    00000058     Serial.o (.text.Serial_Init)
                  000065c4    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000661c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006674    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000066ca    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000671c    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  0000676c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000067bc    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000680c    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006858    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000068a4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000068f0    0000004c     OLED.o (.text.OLED_Printf)
                  0000693c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006988    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000069d2    00000002     --HOLE-- [fill = 0]
                  000069d4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006a1e    00000002     --HOLE-- [fill = 0]
                  00006a20    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006a68    00000048     ADC.o (.text.adc_getValue)
                  00006ab0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006af8    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006b40    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006b88    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006bcc    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006c10    00000044     Task_App.o (.text.Task_Key)
                  00006c54    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006c98    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006cdc    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006d20    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00006d62    00000002     --HOLE-- [fill = 0]
                  00006d64    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006da6    00000002     --HOLE-- [fill = 0]
                  00006da8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00006de8    00000040     Interrupt.o (.text.Interrupt_Init)
                  00006e28    00000040     Task_App.o (.text.Task_GraySensor)
                  00006e68    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006ea8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006ee8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006f28    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006f68    0000003e     Task.o (.text.Task_CMP)
                  00006fa6    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006fe4    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007020    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000705c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007098    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  000070d4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007110    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  0000714c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00007188    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000071c4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00007200    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000723c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00007276    00000002     --HOLE-- [fill = 0]
                  00007278    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000072b2    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  000072ea    00000002     --HOLE-- [fill = 0]
                  000072ec    00000038     Task_App.o (.text.Task_LED)
                  00007324    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0000735c    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007390    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000073c4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000073f8    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  0000742c    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  0000745e    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007490    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  000074c0    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000074f0    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007520    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007550    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007580    00000030            : vsnprintf.c.obj (.text._outs)
                  000075b0    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000075e0    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007610    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000763c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007668    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007694    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  000076c0    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000076ea    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007712    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000773a    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007762    00000002     --HOLE-- [fill = 0]
                  00007764    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  0000778c    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000077b4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000077dc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007804    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  0000782c    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007854    00000028     SysTick.o (.text.SysTick_Increasment)
                  0000787c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000078a4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000078cc    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000078f2    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007918    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000793e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007964    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007988    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000079ac    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000079d0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000079f2    00000002     --HOLE-- [fill = 0]
                  000079f4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007a14    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007a34    00000020     SysTick.o (.text.Delay)
                  00007a54    00000020     main.o (.text.main)
                  00007a74    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007a94    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007ab2    00000002     --HOLE-- [fill = 0]
                  00007ab4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007ad2    00000002     --HOLE-- [fill = 0]
                  00007ad4    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007af0    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007b0c    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007b28    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007b44    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007b60    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007b7c    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007b98    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007bb4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007bd0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007bec    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007c08    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007c24    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007c40    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007c5c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007c78    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007c94    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007cb0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007ccc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007ce8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007d04    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007d20    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00007d38    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00007d50    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007d68    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007d80    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007d98    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007db0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007dc8    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007de0    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007df8    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007e10    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007e28    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007e40    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007e58    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007e70    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007e88    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00007ea0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007eb8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007ed0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007ee8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007f00    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007f18    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007f30    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007f48    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007f60    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007f78    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007f90    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007fa8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007fc0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007fd8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007ff0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00008008    00000018     OLED.o (.text.DL_I2C_reset)
                  00008020    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00008038    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00008050    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00008068    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008080    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008098    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000080b0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000080c8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000080e0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000080f8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00008110    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00008128    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00008140    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00008158    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008170    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008188    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000081a0    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000081b8    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000081d0    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000081e8    00000018            : vsprintf.c.obj (.text._outs)
                  00008200    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00008216    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  0000822c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00008242    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00008258    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000826e    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00008284    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000829a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000082b0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000082c6    00000016     SysTick.o (.text.SysGetTick)
                  000082dc    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000082f2    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00008306    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  0000831a    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  0000832e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00008342    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00008356    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000836a    00000002     --HOLE-- [fill = 0]
                  0000836c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008380    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008394    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000083a8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000083bc    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000083d0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000083e4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000083f8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000840c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00008420    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00008434    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00008448    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000845a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000846c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000847e    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  0000848e    00000002     --HOLE-- [fill = 0]
                  00008490    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000084a0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000084b0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000084c0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000084d0    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  000084de    00000002     --HOLE-- [fill = 0]
                  000084e0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000084ee    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000084fc    0000000e     MPU6050.o (.text.tap_cb)
                  0000850a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008518    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00008524    0000000c     SysTick.o (.text.Sys_GetTick)
                  00008530    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000853a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008544    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008554    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000855e    00000002     --HOLE-- [fill = 0]
                  00008560    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008570    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000857a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008584    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000858e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008598    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000085a8    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  000085b2    0000000a     MPU6050.o (.text.android_orient_cb)
                  000085bc    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000085c4    00000008     Interrupt.o (.text.SysTick_Handler)
                  000085cc    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000085d4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000085dc    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000085e2    00000002     --HOLE-- [fill = 0]
                  000085e4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000085f4    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000085fa    00000006            : exit.c.obj (.text:abort)
                  00008600    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00008604    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008608    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  0000860c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00008610    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008620    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00008624    0000000c     --HOLE-- [fill = 0]

.cinit     0    00009d30    00000078     
                  00009d30    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  00009d7e    00000002     --HOLE-- [fill = 0]
                  00009d80    0000000c     (__TI_handler_table)
                  00009d8c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009d94    00000010     (__TI_cinit_table)
                  00009da4    00000004     --HOLE-- [fill = 0]

.rodata    0    00008630    00001700     
                  00008630    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009226    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009816    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009a3e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009a40    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009b41    00000007     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009b48    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009b88    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009bb0    00000028     inv_mpu.o (.rodata.test)
                  00009bd8    0000001f     Task_App.o (.rodata.str1.13861004553356644102.1)
                  00009bf7    0000001e     inv_mpu.o (.rodata.reg)
                  00009c15    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00009c18    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009c30    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009c48    00000014     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009c5c    00000014     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00009c70    00000014     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009c84    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009c95    00000011     Task_App.o (.rodata.str1.13166305789289702848.1)
                  00009ca6    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00009cb7    0000000f     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00009cc6    0000000c     inv_mpu.o (.rodata.hw)
                  00009cd2    0000000c     Task_App.o (.rodata.str1.7950429023856218820.1)
                  00009cde    0000000b     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00009ce9    00000001     --HOLE-- [fill = 0]
                  00009cea    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00009cf4    00000009     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009cfd    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009cff    00000001     --HOLE-- [fill = 0]
                  00009d00    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00009d08    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00009d10    00000008     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009d18    00000006     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009d1e    00000005     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009d23    00000004     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009d27    00000004     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009d2b    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009d2d    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000135     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000002     Task_App.o (.data.Task_Motor_PID.startup_counter)
                  20200504    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200505    00000001     Task_App.o (.data.Gray_Digtal)
                  20200506    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200507    00000001     Task.o (.data.Task_Num)
                  20200508    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1588    189       243    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2058    189       249    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2468    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          712     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8218    2072      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     34104   6189      1800   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009d94 records: 2, size/record: 8, table size: 16
	.data: load addr=00009d30, load size=0000004e bytes, run addr=202003d4, run size=00000135 bytes, compression=lzss
	.bss: load addr=00009d8c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009d80 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002515     00008544     00008542   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000043c1     00008560     0000855c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008578          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000858c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000085c2          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000085f8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003ce9     00008598     00008596   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000251f     000085e4     000085e0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000860a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000078a5     00008610     0000860c   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00008601  ADC0_IRQHandler                      
00008601  ADC1_IRQHandler                      
00008601  AES_IRQHandler                       
00008604  C$$EXIT                              
00008601  CANFD0_IRQHandler                    
00008601  DAC0_IRQHandler                      
00006da9  DL_ADC12_setClockConfig              
00008531  DL_Common_delayCycles                
00006859  DL_DMA_initChannel                   
00006345  DL_I2C_fillControllerTXFIFO          
00007099  DL_I2C_flushControllerTXFIFO         
0000793f  DL_I2C_setClockConfig                
00004669  DL_SYSCTL_configSYSPLL               
00005f75  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006b89  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003efd  DL_Timer_initFourCCPWMMode           
00007cb1  DL_Timer_setCaptCompUpdateMethod     
000080c9  DL_Timer_setCaptureCompareOutCtl     
000084a1  DL_Timer_setCaptureCompareValue      
00007ccd  DL_Timer_setClockConfig              
00006a21  DL_UART_init                         
00008449  DL_UART_setClockConfig               
00008601  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00008601  Default_Handler                      
00007a35  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200504  Flag_MPU6050_Ready                   
00008601  GROUP0_IRQHandler                    
000042dd  GROUP1_IRQHandler                    
00004745  Get_Analog_value                     
00007111  Get_Anolog_Value                     
000084d1  Get_Digtal_For_User                  
000072b3  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200505  Gray_Digtal                          
202004a0  Gray_Normal                          
00008605  HOSTexit                             
00008601  HardFault_Handler                    
00008601  I2C0_IRQHandler                      
00008601  I2C1_IRQHandler                      
00005dd7  I2C_OLED_Clear                       
0000714d  I2C_OLED_Set_Pos                     
00005455  I2C_OLED_WR_Byte                     
00006105  I2C_OLED_i2c_sda_unlock              
00006de9  Interrupt_Init                       
00006165  Key_Read                             
00002ca1  MPU6050_Init                         
202004e0  Motor                                
0000594d  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
00005279  Motor_SetDuty                        
00005a49  Motor_Start                          
00005c8d  MyPrintf_DMA                         
00008601  NMI_Handler                          
000026a9  No_MCU_Ganv_Sensor_Init              
00005c19  No_MCU_Ganv_Sensor_Init_Frist        
00006d21  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003bd9  OLED_Init                            
000068f1  OLED_Printf                          
00003189  OLED_ShowChar                        
00005cfd  OLED_ShowString                      
000076c1  PID_IQ_Init                          
00003639  PID_IQ_Prosc                         
00006bcd  PID_IQ_SetParams                     
00008601  PendSV_Handler                       
00008601  RTC_IRQHandler                       
0000159d  Read_Quad                            
0000860d  Reset_Handler                        
00008601  SPI0_IRQHandler                      
00008601  SPI1_IRQHandler                      
00008601  SVC_Handler                          
0000693d  SYSCFG_DL_ADC1_init                  
000074f1  SYSCFG_DL_DMA_CH_RX_init             
00008189  SYSCFG_DL_DMA_CH_TX_init             
00008519  SYSCFG_DL_DMA_init                   
00001e09  SYSCFG_DL_GPIO_init                  
00006515  SYSCFG_DL_I2C_MPU6050_init           
00005fd9  SYSCFG_DL_I2C_OLED_init              
0000561d  SYSCFG_DL_Motor_PWM_init             
000063a5  SYSCFG_DL_SYSCTL_init                
000084b1  SYSCFG_DL_SYSTICK_init               
000057c1  SYSCFG_DL_UART0_init                 
00007611  SYSCFG_DL_init                       
00005319  SYSCFG_DL_initPower                  
0000656d  Serial_Init                          
20200000  Serial_RxData                        
000082c7  SysGetTick                           
000085c5  SysTick_Handler                      
00007855  SysTick_Increasment                  
00008525  Sys_GetTick                          
00008601  TIMA0_IRQHandler                     
00008601  TIMA1_IRQHandler                     
00008601  TIMG0_IRQHandler                     
00008601  TIMG12_IRQHandler                    
00008601  TIMG6_IRQHandler                     
00008601  TIMG7_IRQHandler                     
00008601  TIMG8_IRQHandler                     
0000845b  TI_memcpy_small                      
0000850b  TI_memset_small                      
00004e71  Task_Add                             
00006e29  Task_GraySensor                      
000061c5  Task_IdleFunction                    
0000375d  Task_Init                            
00006c11  Task_Key                             
000072ed  Task_LED                             
00003ac5  Task_Motor_PID                       
00004589  Task_OLED                            
00004f25  Task_Serial                          
000021c5  Task_Start                           
000033e9  Task_Tracker                         
00008601  UART0_IRQHandler                     
00008601  UART1_IRQHandler                     
00008601  UART2_IRQHandler                     
00008601  UART3_IRQHandler                     
000081a1  _IQ24div                             
000081b9  _IQ24mpy                             
00007521  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009d94  __TI_CINIT_Base                      
00009da4  __TI_CINIT_Limit                     
00009da4  __TI_CINIT_Warm                      
00009d80  __TI_Handler_Table_Base              
00009d8c  __TI_Handler_Table_Limit             
00007201  __TI_auto_init_nobinit_nopinit       
000059cd  __TI_decompress_lzss                 
0000846d  __TI_decompress_none                 
000065c5  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000082dd  __TI_zero_init_nomemset              
0000251f  __adddf3                             
00004903  __addsf3                             
00009a40  __aeabi_ctype_table_                 
00009a40  __aeabi_ctype_table_C                
00005b31  __aeabi_d2f                          
000069d5  __aeabi_d2iz                         
00006d65  __aeabi_d2uiz                        
0000251f  __aeabi_dadd                         
0000603d  __aeabi_dcmpeq                       
00006079  __aeabi_dcmpge                       
0000608d  __aeabi_dcmpgt                       
00006065  __aeabi_dcmple                       
00006051  __aeabi_dcmplt                       
00003ce9  __aeabi_ddiv                         
000043c1  __aeabi_dmul                         
00002515  __aeabi_dsub                         
202004f4  __aeabi_errno                        
000085cd  __aeabi_errno_addr                   
00006ea9  __aeabi_f2d                          
00007325  __aeabi_f2iz                         
00004903  __aeabi_fadd                         
000060a1  __aeabi_fcmpeq                       
000060dd  __aeabi_fcmpge                       
000060f1  __aeabi_fcmpgt                       
000060c9  __aeabi_fcmple                       
000060b5  __aeabi_fcmplt                       
000058c9  __aeabi_fdiv                         
000056a9  __aeabi_fmul                         
000048f9  __aeabi_fsub                         
00007669  __aeabi_i2d                          
00007189  __aeabi_i2f                          
00006675  __aeabi_idiv                         
000026a7  __aeabi_idiv0                        
00006675  __aeabi_idivmod                      
000051d3  __aeabi_ldiv0                        
00007ab5  __aeabi_llsl                         
000079ad  __aeabi_lmul                         
000085d5  __aeabi_memcpy                       
000085d5  __aeabi_memcpy4                      
000085d5  __aeabi_memcpy8                      
000084e1  __aeabi_memset                       
000084e1  __aeabi_memset4                      
000084e1  __aeabi_memset8                      
00007989  __aeabi_ui2d                         
0000787d  __aeabi_ui2f                         
00006e69  __aeabi_uidiv                        
00006e69  __aeabi_uidivmod                     
000083f9  __aeabi_uldivmod                     
00007ab5  __ashldi3                            
ffffffff  __binit__                            
00005e41  __cmpdf2                             
0000723d  __cmpsf2                             
00003ce9  __divdf3                             
000058c9  __divsf3                             
00005e41  __eqdf2                              
0000723d  __eqsf2                              
00006ea9  __extendsfdf2                        
000069d5  __fixdfsi                            
00007325  __fixsfsi                            
00006d65  __fixunsdfsi                         
00007669  __floatsidf                          
00007189  __floatsisf                          
00007989  __floatunsidf                        
0000787d  __floatunsisf                        
00005abd  __gedf2                              
000071c5  __gesf2                              
00005abd  __gtdf2                              
000071c5  __gtsf2                              
00005e41  __ledf2                              
0000723d  __lesf2                              
00005e41  __ltdf2                              
0000723d  __ltsf2                              
UNDEFED   __mpu_init                           
000043c1  __muldf3                             
000079ad  __muldi3                             
00007279  __muldsi3                            
000056a9  __mulsf3                             
00005e41  __nedf2                              
0000723d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002515  __subdf3                             
000048f9  __subsf3                             
00005b31  __truncdfsf2                         
000051d5  __udivmoddi4                         
000078a5  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008621  _system_pre_init                     
000085fb  abort                                
00006a69  adc_getValue                         
00009816  asc2_0806                            
00009226  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002831  atan2                                
00002831  atan2l                               
00000df5  atanl                                
00006ee9  atoi                                 
ffffffff  binit                                
00005d6b  convertAnalogToDigital               
202004f8  delayTick                            
00006ab1  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00006225  dmp_enable_gyro_cal                  
00006af9  dmp_enable_lp_quat                   
00007d05  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
0000840d  dmp_register_android_orient_cb       
00008421  dmp_register_tap_cb                  
000054ed  dmp_set_fifo_rate                    
000029b9  dmp_set_orientation                  
00006c55  dmp_set_shake_reject_thresh          
0000742d  dmp_set_shake_reject_time            
0000745f  dmp_set_shake_reject_timeout         
00005f0f  dmp_set_tap_axes                     
00006c99  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
000075b1  dmp_set_tap_time                     
000075e1  dmp_set_tap_time_multi               
20200508  enable_group1_irq                    
00006401  frexp                                
00006401  frexpl                               
00009cc6  hw                                   
00000000  interruptVectors                     
00004821  ldexp                                
00004821  ldexpl                               
00007a55  main                                 
000079d1  memccpy                              
00007a75  memcmp                               
202003d2  more                                 
00006285  mpu6050_i2c_sda_unlock               
00004cfd  mpu_configure_fifo                   
00005ba5  mpu_get_accel_fsr                    
000062e5  mpu_get_gyro_fsr                     
000073f9  mpu_get_sample_rate                  
00003511  mpu_init                             
00003881  mpu_load_firmware                    
00004001  mpu_lp_accel_mode                    
00003df5  mpu_read_fifo_stream                 
00004fd1  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
000044a5  mpu_set_accel_fsr                    
00002375  mpu_set_bypass                       
00004db9  mpu_set_dmp_state                    
00004b75  mpu_set_gyro_fsr                     
000053b9  mpu_set_int_latched                  
00004aa5  mpu_set_lpf                          
000041f1  mpu_set_sample_rate                  
000032b9  mpu_set_sensors                      
0000507d  mpu_write_mem                        
00002f21  mspm0_i2c_read                       
00004c39  mspm0_i2c_write                      
00005129  normalizeAnalogValues                
00003055  qsort                                
202003a0  quat                                 
00009bf7  reg                                  
00004821  scalbn                               
00004821  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002b31  sqrt                                 
00002b31  sqrtl                                
00009bb0  test                                 
202004fc  uwTick                               
00006f29  vsnprintf                            
00007695  vsprintf                             
000084c1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001e09  SYSCFG_DL_GPIO_init                  
000021c5  Task_Start                           
00002375  mpu_set_bypass                       
00002515  __aeabi_dsub                         
00002515  __subdf3                             
0000251f  __adddf3                             
0000251f  __aeabi_dadd                         
000026a7  __aeabi_idiv0                        
000026a9  No_MCU_Ganv_Sensor_Init              
00002831  atan2                                
00002831  atan2l                               
000029b9  dmp_set_orientation                  
00002b31  sqrt                                 
00002b31  sqrtl                                
00002ca1  MPU6050_Init                         
00002f21  mspm0_i2c_read                       
00003055  qsort                                
00003189  OLED_ShowChar                        
000032b9  mpu_set_sensors                      
000033e9  Task_Tracker                         
00003511  mpu_init                             
00003639  PID_IQ_Prosc                         
0000375d  Task_Init                            
00003881  mpu_load_firmware                    
00003ac5  Task_Motor_PID                       
00003bd9  OLED_Init                            
00003ce9  __aeabi_ddiv                         
00003ce9  __divdf3                             
00003df5  mpu_read_fifo_stream                 
00003efd  DL_Timer_initFourCCPWMMode           
00004001  mpu_lp_accel_mode                    
000041f1  mpu_set_sample_rate                  
000042dd  GROUP1_IRQHandler                    
000043c1  __aeabi_dmul                         
000043c1  __muldf3                             
000044a5  mpu_set_accel_fsr                    
00004589  Task_OLED                            
00004669  DL_SYSCTL_configSYSPLL               
00004745  Get_Analog_value                     
00004821  ldexp                                
00004821  ldexpl                               
00004821  scalbn                               
00004821  scalbnl                              
000048f9  __aeabi_fsub                         
000048f9  __subsf3                             
00004903  __addsf3                             
00004903  __aeabi_fadd                         
00004aa5  mpu_set_lpf                          
00004b75  mpu_set_gyro_fsr                     
00004c39  mspm0_i2c_write                      
00004cfd  mpu_configure_fifo                   
00004db9  mpu_set_dmp_state                    
00004e71  Task_Add                             
00004f25  Task_Serial                          
00004fd1  mpu_read_mem                         
0000507d  mpu_write_mem                        
00005129  normalizeAnalogValues                
000051d3  __aeabi_ldiv0                        
000051d5  __udivmoddi4                         
00005279  Motor_SetDuty                        
00005319  SYSCFG_DL_initPower                  
000053b9  mpu_set_int_latched                  
00005455  I2C_OLED_WR_Byte                     
000054ed  dmp_set_fifo_rate                    
0000561d  SYSCFG_DL_Motor_PWM_init             
000056a9  __aeabi_fmul                         
000056a9  __mulsf3                             
000057c1  SYSCFG_DL_UART0_init                 
000058c9  __aeabi_fdiv                         
000058c9  __divsf3                             
0000594d  Motor_GetSpeed                       
000059cd  __TI_decompress_lzss                 
00005a49  Motor_Start                          
00005abd  __gedf2                              
00005abd  __gtdf2                              
00005b31  __aeabi_d2f                          
00005b31  __truncdfsf2                         
00005ba5  mpu_get_accel_fsr                    
00005c19  No_MCU_Ganv_Sensor_Init_Frist        
00005c8d  MyPrintf_DMA                         
00005cfd  OLED_ShowString                      
00005d6b  convertAnalogToDigital               
00005dd7  I2C_OLED_Clear                       
00005e41  __cmpdf2                             
00005e41  __eqdf2                              
00005e41  __ledf2                              
00005e41  __ltdf2                              
00005e41  __nedf2                              
00005f0f  dmp_set_tap_axes                     
00005f75  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005fd9  SYSCFG_DL_I2C_OLED_init              
0000603d  __aeabi_dcmpeq                       
00006051  __aeabi_dcmplt                       
00006065  __aeabi_dcmple                       
00006079  __aeabi_dcmpge                       
0000608d  __aeabi_dcmpgt                       
000060a1  __aeabi_fcmpeq                       
000060b5  __aeabi_fcmplt                       
000060c9  __aeabi_fcmple                       
000060dd  __aeabi_fcmpge                       
000060f1  __aeabi_fcmpgt                       
00006105  I2C_OLED_i2c_sda_unlock              
00006165  Key_Read                             
000061c5  Task_IdleFunction                    
00006225  dmp_enable_gyro_cal                  
00006285  mpu6050_i2c_sda_unlock               
000062e5  mpu_get_gyro_fsr                     
00006345  DL_I2C_fillControllerTXFIFO          
000063a5  SYSCFG_DL_SYSCTL_init                
00006401  frexp                                
00006401  frexpl                               
00006515  SYSCFG_DL_I2C_MPU6050_init           
0000656d  Serial_Init                          
000065c5  __TI_ltoa                            
00006675  __aeabi_idiv                         
00006675  __aeabi_idivmod                      
00006859  DL_DMA_initChannel                   
000068f1  OLED_Printf                          
0000693d  SYSCFG_DL_ADC1_init                  
000069d5  __aeabi_d2iz                         
000069d5  __fixdfsi                            
00006a21  DL_UART_init                         
00006a69  adc_getValue                         
00006ab1  dmp_enable_6x_lp_quat                
00006af9  dmp_enable_lp_quat                   
00006b89  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006bcd  PID_IQ_SetParams                     
00006c11  Task_Key                             
00006c55  dmp_set_shake_reject_thresh          
00006c99  dmp_set_tap_count                    
00006d21  No_Mcu_Ganv_Sensor_Task_Without_tick 
00006d65  __aeabi_d2uiz                        
00006d65  __fixunsdfsi                         
00006da9  DL_ADC12_setClockConfig              
00006de9  Interrupt_Init                       
00006e29  Task_GraySensor                      
00006e69  __aeabi_uidiv                        
00006e69  __aeabi_uidivmod                     
00006ea9  __aeabi_f2d                          
00006ea9  __extendsfdf2                        
00006ee9  atoi                                 
00006f29  vsnprintf                            
00007099  DL_I2C_flushControllerTXFIFO         
00007111  Get_Anolog_Value                     
0000714d  I2C_OLED_Set_Pos                     
00007189  __aeabi_i2f                          
00007189  __floatsisf                          
000071c5  __gesf2                              
000071c5  __gtsf2                              
00007201  __TI_auto_init_nobinit_nopinit       
0000723d  __cmpsf2                             
0000723d  __eqsf2                              
0000723d  __lesf2                              
0000723d  __ltsf2                              
0000723d  __nesf2                              
00007279  __muldsi3                            
000072b3  Get_Normalize_For_User               
000072ed  Task_LED                             
00007325  __aeabi_f2iz                         
00007325  __fixsfsi                            
000073f9  mpu_get_sample_rate                  
0000742d  dmp_set_shake_reject_time            
0000745f  dmp_set_shake_reject_timeout         
000074f1  SYSCFG_DL_DMA_CH_RX_init             
00007521  _IQ24toF                             
000075b1  dmp_set_tap_time                     
000075e1  dmp_set_tap_time_multi               
00007611  SYSCFG_DL_init                       
00007669  __aeabi_i2d                          
00007669  __floatsidf                          
00007695  vsprintf                             
000076c1  PID_IQ_Init                          
00007855  SysTick_Increasment                  
0000787d  __aeabi_ui2f                         
0000787d  __floatunsisf                        
000078a5  _c_int00_noargs                      
0000793f  DL_I2C_setClockConfig                
00007989  __aeabi_ui2d                         
00007989  __floatunsidf                        
000079ad  __aeabi_lmul                         
000079ad  __muldi3                             
000079d1  memccpy                              
00007a35  Delay                                
00007a55  main                                 
00007a75  memcmp                               
00007ab5  __aeabi_llsl                         
00007ab5  __ashldi3                            
00007cb1  DL_Timer_setCaptCompUpdateMethod     
00007ccd  DL_Timer_setClockConfig              
00007d05  dmp_load_motion_driver_firmware      
000080c9  DL_Timer_setCaptureCompareOutCtl     
00008189  SYSCFG_DL_DMA_CH_TX_init             
000081a1  _IQ24div                             
000081b9  _IQ24mpy                             
000082c7  SysGetTick                           
000082dd  __TI_zero_init_nomemset              
000083f9  __aeabi_uldivmod                     
0000840d  dmp_register_android_orient_cb       
00008421  dmp_register_tap_cb                  
00008449  DL_UART_setClockConfig               
0000845b  TI_memcpy_small                      
0000846d  __TI_decompress_none                 
000084a1  DL_Timer_setCaptureCompareValue      
000084b1  SYSCFG_DL_SYSTICK_init               
000084c1  wcslen                               
000084d1  Get_Digtal_For_User                  
000084e1  __aeabi_memset                       
000084e1  __aeabi_memset4                      
000084e1  __aeabi_memset8                      
0000850b  TI_memset_small                      
00008519  SYSCFG_DL_DMA_init                   
00008525  Sys_GetTick                          
00008531  DL_Common_delayCycles                
000085c5  SysTick_Handler                      
000085cd  __aeabi_errno_addr                   
000085d5  __aeabi_memcpy                       
000085d5  __aeabi_memcpy4                      
000085d5  __aeabi_memcpy8                      
000085fb  abort                                
00008601  ADC0_IRQHandler                      
00008601  ADC1_IRQHandler                      
00008601  AES_IRQHandler                       
00008601  CANFD0_IRQHandler                    
00008601  DAC0_IRQHandler                      
00008601  DMA_IRQHandler                       
00008601  Default_Handler                      
00008601  GROUP0_IRQHandler                    
00008601  HardFault_Handler                    
00008601  I2C0_IRQHandler                      
00008601  I2C1_IRQHandler                      
00008601  NMI_Handler                          
00008601  PendSV_Handler                       
00008601  RTC_IRQHandler                       
00008601  SPI0_IRQHandler                      
00008601  SPI1_IRQHandler                      
00008601  SVC_Handler                          
00008601  TIMA0_IRQHandler                     
00008601  TIMA1_IRQHandler                     
00008601  TIMG0_IRQHandler                     
00008601  TIMG12_IRQHandler                    
00008601  TIMG6_IRQHandler                     
00008601  TIMG7_IRQHandler                     
00008601  TIMG8_IRQHandler                     
00008601  UART0_IRQHandler                     
00008601  UART1_IRQHandler                     
00008601  UART2_IRQHandler                     
00008601  UART3_IRQHandler                     
00008604  C$$EXIT                              
00008605  HOSTexit                             
0000860d  Reset_Handler                        
00008621  _system_pre_init                     
00009226  asc2_1608                            
00009816  asc2_0806                            
00009a40  __aeabi_ctype_table_                 
00009a40  __aeabi_ctype_table_C                
00009bb0  test                                 
00009bf7  reg                                  
00009cc6  hw                                   
00009d80  __TI_Handler_Table_Base              
00009d8c  __TI_Handler_Table_Limit             
00009d94  __TI_CINIT_Base                      
00009da4  __TI_CINIT_Limit                     
00009da4  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
202004f4  __aeabi_errno                        
202004f8  delayTick                            
202004fc  uwTick                               
20200504  Flag_MPU6050_Ready                   
20200505  Gray_Digtal                          
20200508  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[326 symbols]
